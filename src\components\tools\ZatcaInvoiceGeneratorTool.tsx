'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

import { Receipt, Plus, Trash2, Download, Printer, RefreshCw, Save } from 'lucide-react';

interface InvoiceItem {
  description: string;
  quantity: number;
  price: number;
  total: number;
}

interface InvoiceData {
  sellerName: string;
  sellerVAT: string;
  sellerCR: string;
  sellerAddress: string;
  buyerName: string;
  buyerVAT: string;
  buyerCR: string;
  invoiceNumber: string;
  invoiceDate: string;
  items: InvoiceItem[];
}

const VAT_RATE = 0.15; // 15% VAT

export function ZatcaInvoiceGeneratorTool() {
  const [invoiceData, setInvoiceData] = useState<InvoiceData>({
    sellerName: '',
    sellerVAT: '',
    sellerCR: '',
    sellerAddress: '',
    buyerName: '',
    buyerVAT: '',
    buyerCR: '',
    invoiceNumber: '',
    invoiceDate: new Date().toISOString().split('T')[0],
    items: []
  });

  // Load saved seller data from localStorage on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedSellerData = localStorage.getItem('zatca-seller-data');
      if (savedSellerData) {
        try {
          const parsed = JSON.parse(savedSellerData);
          setInvoiceData(prev => ({
            ...prev,
            sellerName: parsed.sellerName || '',
            sellerVAT: parsed.sellerVAT || '',
            sellerCR: parsed.sellerCR || '',
            sellerAddress: parsed.sellerAddress || ''
          }));
        } catch (error) {
          console.error('Error loading saved seller data:', error);
        }
      }
    }
  }, []);

  // Save seller data to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined' && invoiceData.sellerName) {
      const sellerData = {
        sellerName: invoiceData.sellerName,
        sellerVAT: invoiceData.sellerVAT,
        sellerCR: invoiceData.sellerCR,
        sellerAddress: invoiceData.sellerAddress
      };
      localStorage.setItem('zatca-seller-data', JSON.stringify(sellerData));
    }
  }, [invoiceData.sellerName, invoiceData.sellerVAT, invoiceData.sellerCR, invoiceData.sellerAddress]);

  const [currentItem, setCurrentItem] = useState({
    description: '',
    quantity: 1,
    price: 0
  });

  const [showPreview, setShowPreview] = useState(false);
  const [showItemModal, setShowItemModal] = useState(false);
  const invoiceRef = useRef<HTMLDivElement>(null);

  // Generate automatic invoice number
  const generateInvoiceNumber = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const time = String(today.getHours()).padStart(2, '0') + String(today.getMinutes()).padStart(2, '0');
    return `INV-${year}${month}${day}-${time}`;
  };

  // Set automatic invoice number if empty
  useEffect(() => {
    if (!invoiceData.invoiceNumber) {
      setInvoiceData(prev => ({
        ...prev,
        invoiceNumber: generateInvoiceNumber()
      }));
    }
  }, [invoiceData.invoiceNumber]);

  const addItem = () => {
    // Validation
    const errors = [];
    if (!currentItem.description.trim()) errors.push('وصف المنتج');
    if (!currentItem.quantity || currentItem.quantity <= 0) errors.push('كمية صحيحة');
    if (!currentItem.price || currentItem.price <= 0) errors.push('سعر صحيح');

    if (errors.length > 0) {
      alert(`يرجى إدخال:\n• ${errors.join('\n• ')}`);
      return;
    }

    const newItem: InvoiceItem = {
      description: currentItem.description.trim(),
      quantity: currentItem.quantity,
      price: currentItem.price,
      total: currentItem.quantity * currentItem.price
    };

    setInvoiceData(prev => ({
      ...prev,
      items: [...prev.items, newItem]
    }));

    setCurrentItem({ description: '', quantity: 1, price: 0 });
    setShowItemModal(false);
  };

  const removeItem = (index: number) => {
    setInvoiceData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  const generateInvoice = () => {
    // Validation
    const errors = [];
    if (!invoiceData.sellerName.trim()) errors.push('اسم البائع');
    if (!invoiceData.sellerVAT.trim()) errors.push('الرقم الضريبي للبائع');
    if (!invoiceData.sellerAddress.trim()) errors.push('عنوان البائع');
    if (!invoiceData.invoiceNumber.trim()) errors.push('رقم الفاتورة');
    if (invoiceData.items.length === 0) errors.push('منتج واحد على الأقل');

    if (errors.length > 0) {
      alert(`يرجى ملء الحقول التالية:\n• ${errors.join('\n• ')}`);
      return;
    }

    // Validate VAT number format (should be 15 digits)
    if (invoiceData.sellerVAT.length !== 15 || !/^\d+$/.test(invoiceData.sellerVAT)) {
      alert('الرقم الضريبي يجب أن يكون 15 رقماً');
      return;
    }

    setShowPreview(true);
  };

  const generateQRData = () => {
    const dateTime = `${invoiceData.invoiceDate}T${new Date().toTimeString().split(' ')[0]}`;
    const subtotal = invoiceData.items.reduce((sum, item) => sum + item.total, 0);
    const vatAmount = subtotal * VAT_RATE;
    const total = subtotal + vatAmount;

    // ZATCA QR Code format (TLV Base64)
    function toHex(str: string) {
      let hex = '';
      for (let i = 0; i < str.length; i++) {
        hex += str.charCodeAt(i).toString(16).padStart(2, '0');
      }
      return hex;
    }

    function createTLV(tag: number, value: string) {
      const tagHex = tag.toString(16).padStart(2, '0');
      const valueHex = toHex(value);
      const lengthHex = (valueHex.length / 2).toString(16).padStart(2, '0');
      return tagHex + lengthHex + valueHex;
    }

    const tlvData = 
      createTLV(1, invoiceData.sellerName) +
      createTLV(2, invoiceData.sellerVAT) +
      createTLV(3, dateTime) +
      createTLV(4, total.toFixed(2)) +
      createTLV(5, vatAmount.toFixed(2));

    const bytes = [];
    for (let i = 0; i < tlvData.length; i += 2) {
      bytes.push(parseInt(tlvData.substring(i, i + 2), 16));
    }
    return btoa(String.fromCharCode.apply(null, bytes));
  };

  const printInvoice = () => {
    window.print();
  };

  const downloadPDF = async () => {
    if (typeof window !== 'undefined') {
      try {
        // For now, we'll use the browser's print to PDF functionality
        window.print();
      } catch (error) {
        console.error('Error downloading PDF:', error);
        alert('حدث خطأ في تحميل PDF. يرجى استخدام خيار الطباعة من المتصفح.');
      }
    }
  };

  const clearInvoice = () => {
    if (confirm('هل أنت متأكد من مسح جميع بيانات الفاتورة؟')) {
      setInvoiceData(prev => ({
        ...prev,
        buyerName: '',
        buyerVAT: '',
        buyerCR: '',
        invoiceNumber: generateInvoiceNumber(),
        invoiceDate: new Date().toISOString().split('T')[0],
        items: []
      }));
      setShowPreview(false);
    }
  };

  const saveAsTemplate = () => {
    if (typeof window !== 'undefined') {
      const template = {
        sellerName: invoiceData.sellerName,
        sellerVAT: invoiceData.sellerVAT,
        sellerCR: invoiceData.sellerCR,
        sellerAddress: invoiceData.sellerAddress,
        items: invoiceData.items
      };
      localStorage.setItem('zatca-invoice-template', JSON.stringify(template));
      alert('تم حفظ القالب بنجاح');
    }
  };

  const loadTemplate = () => {
    if (typeof window !== 'undefined') {
      const savedTemplate = localStorage.getItem('zatca-invoice-template');
      if (savedTemplate) {
        try {
          const template = JSON.parse(savedTemplate);
          setInvoiceData(prev => ({
            ...prev,
            ...template,
            invoiceNumber: generateInvoiceNumber(),
            invoiceDate: new Date().toISOString().split('T')[0],
            buyerName: '',
            buyerVAT: '',
            buyerCR: ''
          }));
          alert('تم تحميل القالب بنجاح');
        } catch (error) {
          alert('حدث خطأ في تحميل القالب');
        }
      } else {
        alert('لا يوجد قالب محفوظ');
      }
    }
  };

  const subtotal = invoiceData.items.reduce((sum, item) => sum + item.total, 0);
  const vatAmount = subtotal * VAT_RATE;
  const total = subtotal + vatAmount;

  return (
    <>
      <style jsx global>{`
        @media print {
          body * {
            visibility: hidden;
          }
          .invoice-preview, .invoice-preview * {
            visibility: visible;
          }
          .invoice-preview {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            background: white !important;
          }
          .print\\:hidden {
            display: none !important;
          }
          .invoice-container {
            border: 1px solid #000 !important;
            box-shadow: none !important;
          }
        }

        .invoice-container {
          font-family: 'Arial', sans-serif;
          direction: rtl;
          text-align: right;
          background: white;
          border: 2px solid #9ca3af;
        }

        .invoice-container .grid-item {
          border: 2px solid #9ca3af;
        }

        .invoice-container .section-header {
          background-color: #10b981;
          color: white;
          font-weight: bold;
        }

        .invoice-container .section-content {
          background-color: #f0fdf4;
        }

        .invoice-container .total-box {
          border: 2px solid #14b8a6;
          background-color: #f0fdfa;
        }

        .invoice-container .vat-box {
          border: 2px solid #10b981;
          background-color: #f0fdf4;
        }

        .invoice-container .final-total-box {
          border: 2px solid #10b981;
          background-color: #dcfce7;
        }
      `}</style>

      <div className="w-full px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8 print:hidden">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Receipt className="w-8 h-8 text-primary" />
            <h1 className="text-3xl font-bold">مولد الفواتير الإلكترونية - ZATCA</h1>
          </div>
          <p className="text-lg text-muted-foreground mb-4">متوافق مع متطلبات هيئة الزكاة والضريبة والجمارك</p>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="font-semibold text-blue-800">✓ متوافق مع ZATCA</div>
                <div className="text-gray-600">يلبي جميع المتطلبات القانونية</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-green-800">✓ رمز QR تلقائي</div>
                <div className="text-gray-600">إنشاء رمز استجابة سريعة معتمد</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-purple-800">✓ حفظ محلي آمن</div>
                <div className="text-gray-600">بياناتك محفوظة في متصفحك فقط</div>
              </div>
            </div>
          </div>
        </div>

      <div className="space-y-8">
        {/* Form Section */}
        <Card className="w-full">
          <CardHeader>
            <CardTitle className="text-right text-xl">إدخال بيانات الفاتورة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Seller Information */}
            <div>
              <h3 className="text-lg font-semibold text-primary mb-4">معلومات البائع</h3>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="sellerName">اسم البائع</Label>
                  <Input
                    id="sellerName"
                    placeholder="اسم الشركة أو المؤسسة"
                    value={invoiceData.sellerName}
                    onChange={(e) => setInvoiceData(prev => ({ ...prev, sellerName: e.target.value }))}
                  />
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="sellerVAT">الرقم الضريبي</Label>
                    <Input
                      id="sellerVAT"
                      placeholder="123456789012345"
                      value={invoiceData.sellerVAT}
                      onChange={(e) => {
                        const value = e.target.value.replace(/\D/g, '').slice(0, 15);
                        setInvoiceData(prev => ({ ...prev, sellerVAT: value }));
                      }}
                      maxLength={15}
                      className={invoiceData.sellerVAT && invoiceData.sellerVAT.length !== 15 ? 'border-red-300' : ''}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      يجب أن يكون 15 رقماً (مطلوب)
                    </p>
                  </div>
                  <div>
                    <Label htmlFor="sellerCR">رقم السجل التجاري</Label>
                    <Input
                      id="sellerCR"
                      placeholder="1234567890"
                      value={invoiceData.sellerCR}
                      onChange={(e) => setInvoiceData(prev => ({ ...prev, sellerCR: e.target.value }))}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="sellerAddress">العنوان</Label>
                  <Input
                    id="sellerAddress"
                    placeholder="المدينة، الشارع، المملكة العربية السعودية"
                    value={invoiceData.sellerAddress}
                    onChange={(e) => setInvoiceData(prev => ({ ...prev, sellerAddress: e.target.value }))}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Buyer Information */}
            <div>
              <h3 className="text-lg font-semibold text-primary mb-4">معلومات المشتري</h3>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="buyerName">اسم المشتري</Label>
                  <Input
                    id="buyerName"
                    placeholder="اسم العميل"
                    value={invoiceData.buyerName}
                    onChange={(e) => setInvoiceData(prev => ({ ...prev, buyerName: e.target.value }))}
                  />
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="buyerVAT">الرقم الضريبي للمشتري</Label>
                    <Input
                      id="buyerVAT"
                      placeholder="اختياري"
                      value={invoiceData.buyerVAT}
                      onChange={(e) => {
                        const value = e.target.value.replace(/\D/g, '').slice(0, 15);
                        setInvoiceData(prev => ({ ...prev, buyerVAT: value }));
                      }}
                      maxLength={15}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      اختياري - إذا تم إدخاله ستكون فاتورة ضريبية كاملة
                    </p>
                  </div>
                  <div>
                    <Label htmlFor="buyerCR">رقم السجل التجاري للمشتري</Label>
                    <Input
                      id="buyerCR"
                      placeholder="اختياري"
                      value={invoiceData.buyerCR}
                      onChange={(e) => setInvoiceData(prev => ({ ...prev, buyerCR: e.target.value }))}
                    />
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Invoice Details */}
            <div>
              <h3 className="text-lg font-semibold text-primary mb-4">تفاصيل الفاتورة</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="invoiceNumber">رقم الفاتورة</Label>
                  <div className="flex gap-2">
                    <Input
                      id="invoiceNumber"
                      placeholder="INV-2023-001"
                      value={invoiceData.invoiceNumber}
                      onChange={(e) => setInvoiceData(prev => ({ ...prev, invoiceNumber: e.target.value }))}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setInvoiceData(prev => ({ ...prev, invoiceNumber: generateInvoiceNumber() }))}
                      title="إنشاء رقم فاتورة جديد"
                    >
                      <RefreshCw className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <div>
                  <Label htmlFor="invoiceDate">تاريخ الفاتورة</Label>
                  <Input
                    id="invoiceDate"
                    type="date"
                    value={invoiceData.invoiceDate}
                    onChange={(e) => setInvoiceData(prev => ({ ...prev, invoiceDate: e.target.value }))}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Items Section */}
            <div>
              <h3 className="text-lg font-semibold text-primary mb-4">المنتجات / الخدمات</h3>
              <Button onClick={() => setShowItemModal(true)} className="mb-4">
                <Plus className="w-4 h-4 ml-2" />
                إضافة منتج أو خدمة
              </Button>

              {invoiceData.items.length > 0 && (
                <div className="space-y-2">
                  {invoiceData.items.map((item, index) => (
                    <div key={index} className="flex justify-between items-center p-3 bg-muted rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium">{item.description}</div>
                        <div className="text-sm text-muted-foreground">
                          الكمية: {item.quantity} × {item.price.toFixed(2)} = {item.total.toFixed(2)} ريال
                        </div>
                      </div>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => removeItem(index)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}

                  {/* Live Total Display */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-4">
                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span>المجموع (بدون ضريبة):</span>
                        <span className="font-semibold">{subtotal.toFixed(2)} ريال</span>
                      </div>
                      <div className="flex justify-between">
                        <span>ضريبة القيمة المضافة (15%):</span>
                        <span className="font-semibold text-green-600">{vatAmount.toFixed(2)} ريال</span>
                      </div>
                      <div className="flex justify-between border-t pt-1 font-bold text-blue-800">
                        <span>الإجمالي:</span>
                        <span>{total.toFixed(2)} ريال</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-3">
              <Button
                onClick={generateInvoice}
                size="lg"
                className="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-4 text-lg"
                disabled={invoiceData.items.length === 0}
              >
                <Receipt className="w-5 h-5 ml-2" />
                إنشاء الفاتورة
              </Button>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                <Button
                  onClick={clearInvoice}
                  variant="outline"
                  size="sm"
                  className="text-red-600 border-red-300 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4 ml-2" />
                  مسح الفاتورة
                </Button>
                <Button
                  onClick={() => setInvoiceData(prev => ({ ...prev, invoiceNumber: generateInvoiceNumber() }))}
                  variant="outline"
                  size="sm"
                >
                  <RefreshCw className="w-4 h-4 ml-2" />
                  رقم جديد
                </Button>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                <Button
                  onClick={saveAsTemplate}
                  variant="outline"
                  size="sm"
                  className="text-blue-600 border-blue-300 hover:bg-blue-50"
                  disabled={!invoiceData.sellerName || invoiceData.items.length === 0}
                >
                  <Save className="w-4 h-4 ml-2" />
                  حفظ كقالب
                </Button>
                <Button
                  onClick={loadTemplate}
                  variant="outline"
                  size="sm"
                  className="text-green-600 border-green-300 hover:bg-green-50"
                >
                  <Download className="w-4 h-4 ml-2" />
                  تحميل قالب
                </Button>
              </div>

              {invoiceData.items.length === 0 && (
                <p className="text-sm text-muted-foreground text-center">
                  يجب إضافة منتج واحد على الأقل لإنشاء الفاتورة
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Preview Section */}
        <Card className="w-full">
          <CardHeader>
            <CardTitle className="text-right text-xl">معاينة الفاتورة</CardTitle>
          </CardHeader>
          <CardContent>
            {!showPreview ? (
              <div className="text-center py-16 text-muted-foreground bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                <Receipt className="w-20 h-20 mx-auto mb-6 opacity-40" />
                <h3 className="text-xl font-medium mb-3 text-gray-700">معاينة الفاتورة ستظهر هنا</h3>
                <p className="text-gray-500 mb-4">قم بملء البيانات المطلوبة وإضافة المنتجات</p>
                <p className="text-sm text-gray-400">ثم اضغط على "إنشاء الفاتورة" لرؤية النتيجة</p>
              </div>
            ) : (
              <div>
                <div ref={invoiceRef} className="invoice-preview">
                  <InvoicePreview
                    invoiceData={invoiceData}
                    subtotal={subtotal}
                    vatAmount={vatAmount}
                    total={total}
                    qrData={generateQRData()}
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 mt-6 print:hidden">
                  <Button onClick={printInvoice} variant="outline" size="lg" className="w-full">
                    <Printer className="w-4 h-4 ml-2" />
                    طباعة
                  </Button>
                  <Button onClick={downloadPDF} variant="outline" size="lg" className="w-full">
                    <Download className="w-4 h-4 ml-2" />
                    تحميل PDF
                  </Button>
                  <Button
                    onClick={clearInvoice}
                    variant="outline"
                    size="lg"
                    className="w-full text-red-600 border-red-300 hover:bg-red-50"
                  >
                    <Trash2 className="w-4 h-4 ml-2" />
                    فاتورة جديدة
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Item Modal */}
      {showItemModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle className="text-right">إضافة منتج / خدمة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="itemDescription">الوصف</Label>
                <Input
                  id="itemDescription"
                  placeholder="وصف المنتج أو الخدمة"
                  value={currentItem.description}
                  onChange={(e) => setCurrentItem(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="itemQuantity">الكمية</Label>
                  <Input
                    id="itemQuantity"
                    type="number"
                    min="1"
                    value={currentItem.quantity}
                    onChange={(e) => setCurrentItem(prev => ({ ...prev, quantity: parseInt(e.target.value) || 1 }))}
                  />
                </div>
                <div>
                  <Label htmlFor="itemPrice">سعر الوحدة (بدون ضريبة)</Label>
                  <Input
                    id="itemPrice"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={currentItem.price}
                    onChange={(e) => setCurrentItem(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                  />
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-2">
                <Button onClick={addItem} className="flex-1">
                  إضافة
                </Button>
                <Button onClick={() => setShowItemModal(false)} variant="outline" className="flex-1">
                  إلغاء
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      </div>
    </>
  );
}

// Invoice Preview Component
interface InvoicePreviewProps {
  invoiceData: InvoiceData;
  subtotal: number;
  vatAmount: number;
  total: number;
  qrData: string;
}

function InvoicePreview({ invoiceData, subtotal, vatAmount, total, qrData }: InvoicePreviewProps) {
  return (
    <div className="invoice-container bg-white p-6 md:p-8 border-2 border-gray-400 rounded-lg shadow-lg max-w-full overflow-x-auto" style={{ fontFamily: 'Arial, sans-serif', direction: 'rtl' }}>

      {/* Header Section */}
      <div className="grid grid-cols-3 gap-4 mb-6">

        {/* QR Code Section - Left */}
        <div className="border-2 border-gray-400 rounded-lg p-4 text-center">
          <div className="bg-white p-2 rounded inline-block mb-2">
            <QRCodeDisplay data={qrData} />
          </div>
        </div>

        {/* Invoice Title - Center */}
        <div className="text-center">
          <div className="border-2 border-green-500 rounded-lg p-4 bg-green-50">
            <h1 className="text-xl font-bold text-green-800 mb-2">فاتورة ضريبية</h1>
          </div>
        </div>

        {/* Date and Invoice Number - Right */}
        <div className="space-y-2">
          <div className="border-2 border-gray-400 rounded-lg p-3 bg-gray-50 text-center">
            <div className="text-sm font-bold">التاريخ</div>
            <div className="text-lg">{new Date(invoiceData.invoiceDate).toLocaleDateString('ar-SA')}</div>
          </div>
          <div className="border-2 border-gray-400 rounded-lg p-3 bg-gray-50 text-center">
            <div className="text-sm font-bold">الرقم التسلسلي</div>
            <div className="text-lg">{invoiceData.invoiceNumber}</div>
          </div>
        </div>
      </div>

      {/* Seller Info Section */}
      <div className="mb-4">
        <div className="border-2 border-green-500 rounded-lg bg-green-50">
          <div className="bg-green-500 text-white text-center py-2 rounded-t-lg">
            <h3 className="font-bold">معلومات البائع</h3>
          </div>
          <div className="grid grid-cols-3 gap-0">
            <div className="border-l-2 border-green-500 p-3 text-center">
              <div className="text-sm font-bold text-green-800">اسم البائع</div>
              <div className="text-sm mt-1">{invoiceData.sellerName}</div>
            </div>
            <div className="border-l-2 border-green-500 p-3 text-center">
              <div className="text-sm font-bold text-green-800">عنوان البائع</div>
              <div className="text-sm mt-1">{invoiceData.sellerAddress}</div>
            </div>
            <div className="p-3 text-center">
              <div className="text-sm font-bold text-green-800">رقم تسجيل ضريبة القيمة المضافة للبائع</div>
              <div className="text-sm mt-1">{invoiceData.sellerVAT}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Buyer Info Section */}
      <div className="mb-4">
        <div className="border-2 border-green-500 rounded-lg bg-green-50">
          <div className="bg-green-500 text-white text-center py-2 rounded-t-lg">
            <h3 className="font-bold">معلومات المشتري</h3>
          </div>
          <div className="grid grid-cols-3 gap-0">
            <div className="border-l-2 border-green-500 p-3 text-center">
              <div className="text-sm font-bold text-green-800">اسم المشتري</div>
              <div className="text-sm mt-1">{invoiceData.buyerName || 'عميل نقدي'}</div>
            </div>
            <div className="border-l-2 border-green-500 p-3 text-center">
              <div className="text-sm font-bold text-green-800">رقم السجل التجاري</div>
              <div className="text-sm mt-1">{invoiceData.buyerCR || '-'}</div>
            </div>
            <div className="p-3 text-center">
              <div className="text-sm font-bold text-green-800">رقم تسجيل ضريبة القيمة المضافة للمشتري</div>
              <div className="text-sm mt-1">{invoiceData.buyerVAT || '-'}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Items Table */}
      <div className="overflow-x-auto mb-4">
        <div className="grid grid-cols-6 gap-0 border-2 border-gray-400 rounded-lg overflow-hidden">

          {/* Headers */}
          <div className="bg-green-100 border-r-2 border-gray-400 p-3 text-center font-bold text-sm">
            البيان
          </div>
          <div className="bg-green-100 border-r-2 border-gray-400 p-3 text-center font-bold text-sm">
            سعر الوحدة
          </div>
          <div className="bg-green-100 border-r-2 border-gray-400 p-3 text-center font-bold text-sm">
            الكمية
          </div>
          <div className="bg-green-100 border-r-2 border-gray-400 p-3 text-center font-bold text-sm">
            المجموع الفرعي بدون الضريبة
          </div>
          <div className="bg-green-100 border-r-2 border-gray-400 p-3 text-center font-bold text-sm">
            نسبة الضريبة
          </div>
          <div className="bg-green-100 p-3 text-center font-bold text-sm">
            المجموع شامل ضريبة القيمة المضافة
          </div>

          {/* Items */}
          {invoiceData.items.map((item, index) => {
            const itemVAT = item.total * VAT_RATE;
            const itemTotalWithVAT = item.total + itemVAT;
            return (
              <>
                <div key={`${index}-desc`} className="border-t-2 border-r-2 border-gray-400 p-3 text-right font-medium text-sm bg-white">
                  {item.description}
                </div>
                <div key={`${index}-price`} className="border-t-2 border-r-2 border-gray-400 p-3 text-center text-sm bg-white">
                  {item.price.toFixed(2)}
                </div>
                <div key={`${index}-qty`} className="border-t-2 border-r-2 border-gray-400 p-3 text-center text-sm bg-white">
                  {item.quantity}
                </div>
                <div key={`${index}-subtotal`} className="border-t-2 border-r-2 border-gray-400 p-3 text-center font-semibold text-sm bg-white">
                  {item.total.toFixed(2)}
                </div>
                <div key={`${index}-vat-rate`} className="border-t-2 border-r-2 border-gray-400 p-3 text-center text-sm bg-white">
                  %15
                </div>
                <div key={`${index}-total`} className="border-t-2 border-gray-400 p-3 text-center font-bold text-sm bg-white">
                  {itemTotalWithVAT.toFixed(2)}
                </div>
              </>
            );
          })}
        </div>
      </div>

      {/* Totals Section */}
      <div className="space-y-3 mb-6">

        {/* Subtotal */}
        <div className="border-2 border-teal-500 rounded-lg bg-teal-50 p-4 text-center">
          <div className="flex justify-between items-center">
            <span className="text-lg font-bold">المجموع</span>
            <span className="text-xl font-bold">{subtotal.toFixed(2)}</span>
          </div>
        </div>

        {/* VAT */}
        <div className="border-2 border-green-500 rounded-lg bg-green-50 p-4 text-center">
          <div className="flex justify-between items-center">
            <span className="text-lg font-bold">ضريبة القيمة المضافة (15%)</span>
            <span className="text-xl font-bold">{vatAmount.toFixed(2)}</span>
          </div>
        </div>

        {/* Total */}
        <div className="border-2 border-green-500 rounded-lg bg-green-100 p-4 text-center">
          <div className="flex justify-between items-center">
            <span className="text-lg font-bold">المجموع مع الضريبة (15%)</span>
            <span className="text-xl font-bold">{total.toFixed(2)}</span>
          </div>
        </div>
      </div>

      {/* Footer Section */}
      <div className="text-center text-sm text-gray-600 border-t-2 border-gray-300 pt-4">
        <p className="font-semibold text-lg mb-2">شكراً لتعاملكم معنا</p>
        <p className="mb-2">هذه فاتورة إلكترونية متوافقة مع متطلبات هيئة الزكاة والضريبة والجمارك</p>
        <p className="text-xs">
          تم إنشاء هذه الفاتورة في: {new Date().toLocaleString('ar-SA')}
        </p>
      </div>
    </div>
  );
}

// QR Code Display Component
function QRCodeDisplay({ data }: { data: string }) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (canvasRef.current && data) {
      // Simple QR code representation using canvas
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        // Clear canvas
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, 128, 128);

        // Draw a simple pattern to represent QR code
        ctx.fillStyle = 'black';
        const size = 4;
        for (let i = 0; i < 32; i++) {
          for (let j = 0; j < 32; j++) {
            // Create a pseudo-random pattern based on data
            const hash = data.charCodeAt((i * 32 + j) % data.length);
            if (hash % 3 === 0) {
              ctx.fillRect(i * size, j * size, size, size);
            }
          }
        }

        // Draw corner markers
        const drawCorner = (x: number, y: number) => {
          ctx.fillRect(x, y, 28, 28);
          ctx.fillStyle = 'white';
          ctx.fillRect(x + 4, y + 4, 20, 20);
          ctx.fillStyle = 'black';
          ctx.fillRect(x + 8, y + 8, 12, 12);
        };

        drawCorner(0, 0);
        drawCorner(100, 0);
        drawCorner(0, 100);
      }
    }
  }, [data]);

  return (
    <div className="w-32 h-32 bg-white flex items-center justify-center border rounded">
      <canvas
        ref={canvasRef}
        width={128}
        height={128}
        className="w-full h-full"
      />
    </div>
  );
}
