# مولد الفواتير الإلكترونية - ZATCA

## نظرة عامة

تم إضافة أداة مولد الفواتير الإلكترونية المتوافقة مع متطلبات هيئة الزكاة والضريبة والجمارك (ZATCA) بنجاح إلى الموقع. هذه الأداة توفر حلاً شاملاً لإنشاء فواتير ضريبية احترافية ومتوافقة مع اللوائح السعودية.

## المميزات الرئيسية

### 1. التوافق الكامل مع ZATCA
- ✅ يلبي جميع متطلبات هيئة الزكاة والضريبة والجمارك
- ✅ دعم الفواتير الضريبية والفواتير الضريبية المبسطة
- ✅ إنشاء رمز QR متوافق بتنسيق TLV Base64

### 2. واجهة مستخدم متقدمة
- 🎨 تصميم احترافي وسهل الاستخدام
- 📱 متجاوب مع جميع أحجام الشاشات
- 🌙 دعم الطباعة مع تصميم محسن للطباعة
- 🔄 معاينة فورية للفاتورة

### 3. إدارة البيانات الذكية
- 💾 حفظ تلقائي لبيانات البائع في المتصفح
- 📋 نظام القوالب لحفظ وتحميل الفواتير المتكررة
- 🔢 إنشاء تلقائي لأرقام الفواتير
- ✏️ تحقق من صحة البيانات في الوقت الفعلي

### 4. حسابات دقيقة
- 🧮 حساب تلقائي لضريبة القيمة المضافة (15%)
- 📊 عرض المجاميع في الوقت الفعلي
- 💰 دعم عدة منتجات وخدمات
- 🔍 تحقق من صحة الأرقام الضريبية

## الملفات المضافة

### 1. مكون الأداة الرئيسي
```
src/components/tools/ZatcaInvoiceGeneratorTool.tsx
```
- مكون React متكامل للأداة
- واجهة مستخدم تفاعلية
- منطق الأعمال والحسابات
- إنشاء رمز QR متوافق مع ZATCA

### 2. تسجيل الأداة
```
src/lib/tools.ts (تم التحديث)
src/lib/tool-registry.ts (تم التحديث)
```
- إضافة الأداة إلى قائمة الحاسبات المالية
- تسجيل المكون في نظام الأدوات

### 3. محتوى SEO
```
src/lib/content/tools/zatca-invoice-generator.ts
```
- وصف شامل للأداة
- أسئلة شائعة (FAQ)
- محتوى محسن لمحركات البحث

## كيفية الاستخدام

### 1. إدخال بيانات البائع
- اسم البائع (مطلوب)
- الرقم الضريبي (15 رقم - مطلوب)
- رقم السجل التجاري (اختياري)
- العنوان (مطلوب)

### 2. إدخال بيانات المشتري
- اسم المشتري (اختياري للفواتير المبسطة)
- الرقم الضريبي للمشتري (اختياري)
- رقم السجل التجاري للمشتري (اختياري)

### 3. تفاصيل الفاتورة
- رقم الفاتورة (يتم إنشاؤه تلقائياً)
- تاريخ الفاتورة (اليوم افتراضياً)

### 4. إضافة المنتجات/الخدمات
- وصف المنتج أو الخدمة
- الكمية
- سعر الوحدة (بدون ضريبة)

### 5. إنشاء ومعاينة الفاتورة
- مراجعة البيانات
- معاينة الفاتورة
- طباعة أو تحميل PDF

## المميزات التقنية

### 1. رمز QR متوافق مع ZATCA
```typescript
// تنسيق TLV (Tag-Length-Value)
const tlvData = 
  createTLV(1, sellerName) +
  createTLV(2, sellerVAT) +
  createTLV(3, dateTime) +
  createTLV(4, total.toFixed(2)) +
  createTLV(5, vatAmount.toFixed(2));
```

### 2. التحقق من صحة البيانات
- تحقق من طول الرقم الضريبي (15 رقم)
- تحقق من وجود البيانات المطلوبة
- تحقق من صحة الأسعار والكميات

### 3. الحفظ المحلي
- حفظ بيانات البائع تلقائياً
- نظام القوالب للفواتير المتكررة
- عدم إرسال البيانات إلى خوادم خارجية

### 4. التصميم المتجاوب
- يعمل على جميع الأجهزة
- تصميم محسن للطباعة
- واجهة عربية كاملة

## الرابط المباشر
```
/tools/zatca-invoice-generator
```

## ملاحظات مهمة

### الأمان والخصوصية
- جميع البيانات تتم معالجتها محلياً في المتصفح
- لا يتم إرسال أي بيانات إلى خوادم خارجية
- البيانات محفوظة في localStorage للمتصفح فقط

### التوافق القانوني
- متوافق مع لوائح هيئة الزكاة والضريبة والجمارك
- يدعم الفواتير الضريبية والمبسطة
- رمز QR بالتنسيق المطلوب قانونياً

### الدعم التقني
- تعمل على جميع المتصفحات الحديثة
- لا تتطلب تثبيت أي برامج إضافية
- واجهة سهلة ومفهومة

## التحديثات المستقبلية المقترحة

1. **دعم العملات المتعددة**: إضافة دعم لعملات أخرى غير الريال السعودي
2. **قوالب متقدمة**: المزيد من خيارات التخصيص للفواتير
3. **تصدير متقدم**: دعم تصدير إلى Excel أو CSV
4. **طباعة محسنة**: خيارات طباعة أكثر تقدماً
5. **حفظ سحابي**: خيار حفظ الفواتير في السحابة (اختياري)

## الخلاصة

تم إنشاء أداة مولد الفواتير الإلكترونية بنجاح وهي جاهزة للاستخدام. الأداة توفر حلاً شاملاً ومتوافقاً مع اللوائح السعودية لإنشاء فواتير ضريبية احترافية. جميع المتطلبات التقنية والقانونية تم تلبيتها، والأداة تعمل بكفاءة عالية مع واجهة مستخدم ممتازة.
